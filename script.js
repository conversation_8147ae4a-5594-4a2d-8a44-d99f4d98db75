// 全局变量
let transactions = JSON.parse(localStorage.getItem('transactions')) || [];
let currentBalance = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    updateDashboard();
    renderTransactions();
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000); // 每秒更新时间
});

// 初始化应用
function initializeApp() {
    // 设置默认日期为当前时间
    const now = new Date();
    const dateTimeString = now.toISOString().slice(0, 16);
    
    document.getElementById('incomeDate').value = dateTimeString;
    document.getElementById('expenseDate').value = dateTimeString;
    
    // 计算当前余额
    calculateBalance();
}

// 设置事件监听器
function setupEventListeners() {
    // 导航菜单点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetPage = this.getAttribute('href').substring(1);
            showPage(targetPage);

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');

            // 在移动端点击导航后关闭侧边栏
            if (window.innerWidth <= 768) {
                closeSidebar();
            }
        });
    });

    // 菜单切换按钮
    const menuToggle = document.getElementById('menuToggle');
    if (menuToggle) {
        menuToggle.addEventListener('click', toggleSidebar);
    }

    // 侧边栏遮罩层点击事件
    const sidebarOverlay = document.getElementById('sidebarOverlay');
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', closeSidebar);
    }

    // 窗口大小改变事件
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
        }
    });

    // 收入表单提交
    document.getElementById('incomeForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addTransaction('income');
    });

    // 支出表单提交
    document.getElementById('expenseForm').addEventListener('submit', function(e) {
        e.preventDefault();
        addTransaction('expense');
    });
}

// 显示指定页面
function showPage(pageId) {
    document.querySelectorAll('.page').forEach(page => {
        page.classList.remove('active');
    });
    document.getElementById(pageId).classList.add('active');
}

// 切换侧边栏显示/隐藏
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');
    const mainContent = document.getElementById('mainContent');

    if (window.innerWidth <= 768) {
        // 移动端：使用遮罩层
        sidebar.classList.toggle('show');
        overlay.classList.toggle('show');
    } else {
        // 桌面端：折叠侧边栏
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    }
}

// 关闭侧边栏
function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.getElementById('sidebarOverlay');

    sidebar.classList.remove('show');
    overlay.classList.remove('show');
}

// 更新当前时间
function updateCurrentTime() {
    const timeElement = document.getElementById('currentTime');
    if (timeElement) {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        timeElement.textContent = timeString;
    }
}

// 添加交易记录
function addTransaction(type) {
    const form = document.getElementById(type + 'Form');
    const formData = new FormData(form);
    
    const transaction = {
        id: Date.now(),
        type: type,
        project: formData.get('project'),
        date: formData.get('date'),
        amount: parseFloat(formData.get('amount')),
        remark: formData.get('remark') || '',
        timestamp: new Date().toISOString()
    };

    // 验证数据
    if (!transaction.project || !transaction.date || !transaction.amount) {
        alert('请填写所有必填字段！');
        return;
    }

    if (transaction.amount <= 0) {
        alert('金额必须大于0！');
        return;
    }

    // 添加到交易列表
    transactions.push(transaction);
    
    // 保存到本地存储
    localStorage.setItem('transactions', JSON.stringify(transactions));
    
    // 重新计算余额
    calculateBalance();
    
    // 更新界面
    updateDashboard();
    renderTransactions();
    
    // 清空表单
    form.reset();
    
    // 重新设置默认时间
    const now = new Date();
    const dateTimeString = now.toISOString().slice(0, 16);
    document.getElementById(type + 'Date').value = dateTimeString;
    
    alert(`${type === 'income' ? '收入' : '支出'}记录添加成功！`);
}

// 计算余额
function calculateBalance() {
    currentBalance = 0;
    
    // 按时间排序交易记录
    const sortedTransactions = [...transactions].sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // 为每个交易计算当时的余额
    sortedTransactions.forEach(transaction => {
        if (transaction.type === 'income') {
            currentBalance += transaction.amount;
        } else {
            currentBalance -= transaction.amount;
        }
        transaction.balance = currentBalance;
    });
}

// 更新仪表盘
function updateDashboard() {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    let monthlyIncome = 0;
    let monthlyExpense = 0;
    
    transactions.forEach(transaction => {
        const transactionDate = new Date(transaction.date);
        if (transactionDate.getMonth() === currentMonth && transactionDate.getFullYear() === currentYear) {
            if (transaction.type === 'income') {
                monthlyIncome += transaction.amount;
            } else {
                monthlyExpense += transaction.amount;
            }
        }
    });

    document.getElementById('totalBalance').textContent = `¥${currentBalance.toFixed(2)}`;
    document.querySelector('.stat-card:nth-child(2) .stat-value').textContent = `¥${monthlyIncome.toFixed(2)}`;
    document.querySelector('.stat-card:nth-child(3) .stat-value').textContent = `¥${monthlyExpense.toFixed(2)}`;
    document.querySelector('.stat-card:nth-child(4) .stat-value').textContent = transactions.length;
}

// 渲染交易记录
function renderTransactions() {
    renderIncomeTable();
    renderExpenseTable();
}

// 渲染收入表格
function renderIncomeTable() {
    const tbody = document.getElementById('incomeTableBody');
    const incomeTransactions = transactions.filter(t => t.type === 'income')
        .sort((a, b) => new Date(b.date) - new Date(a.date));
    
    tbody.innerHTML = '';
    
    incomeTransactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${transaction.project}</td>
            <td>${formatDateTime(transaction.date)}</td>
            <td class="income">¥${transaction.amount.toFixed(2)}</td>
            <td>¥${transaction.balance.toFixed(2)}</td>
            <td>${transaction.remark}</td>
            <td>
                <button class="btn btn-danger" onclick="deleteTransaction(${transaction.id})" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 渲染支出表格
function renderExpenseTable() {
    const tbody = document.getElementById('expenseTableBody');
    const expenseTransactions = transactions.filter(t => t.type === 'expense')
        .sort((a, b) => new Date(b.date) - new Date(a.date));
    
    tbody.innerHTML = '';
    
    expenseTransactions.forEach(transaction => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${transaction.project}</td>
            <td>${formatDateTime(transaction.date)}</td>
            <td class="expense">¥${transaction.amount.toFixed(2)}</td>
            <td>¥${transaction.balance.toFixed(2)}</td>
            <td>${transaction.remark}</td>
            <td>
                <button class="btn btn-danger" onclick="deleteTransaction(${transaction.id})" style="padding: 0.25rem 0.5rem; font-size: 0.8rem;">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 删除交易记录
function deleteTransaction(id) {
    if (confirm('确定要删除这条记录吗？')) {
        transactions = transactions.filter(t => t.id !== id);
        localStorage.setItem('transactions', JSON.stringify(transactions));
        calculateBalance();
        updateDashboard();
        renderTransactions();
        alert('记录删除成功！');
    }
}

// 格式化日期时间
function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 生成报表
function generateReport() {
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    
    if (!startDate || !endDate) {
        alert('请选择开始和结束日期！');
        return;
    }
    
    const filteredTransactions = transactions.filter(transaction => {
        const transactionDate = transaction.date.split('T')[0];
        return transactionDate >= startDate && transactionDate <= endDate;
    });
    
    let totalIncome = 0;
    let totalExpense = 0;
    
    filteredTransactions.forEach(transaction => {
        if (transaction.type === 'income') {
            totalIncome += transaction.amount;
        } else {
            totalExpense += transaction.amount;
        }
    });
    
    const netIncome = totalIncome - totalExpense;
    
    const reportContent = document.getElementById('reportContent');
    reportContent.innerHTML = `
        <h4>报表期间：${startDate} 至 ${endDate}</h4>
        <div class="stats-grid">
            <div class="stat-card">
                <h3>总收入</h3>
                <p class="stat-value income">¥${totalIncome.toFixed(2)}</p>
            </div>
            <div class="stat-card">
                <h3>总支出</h3>
                <p class="stat-value expense">¥${totalExpense.toFixed(2)}</p>
            </div>
            <div class="stat-card">
                <h3>净收入</h3>
                <p class="stat-value ${netIncome >= 0 ? 'income' : 'expense'}">¥${netIncome.toFixed(2)}</p>
            </div>
            <div class="stat-card">
                <h3>交易笔数</h3>
                <p class="stat-value">${filteredTransactions.length}</p>
            </div>
        </div>
    `;
}
