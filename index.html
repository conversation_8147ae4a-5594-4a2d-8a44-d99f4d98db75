<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP出入账系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- 头部导航 -->
        <header class="header">
            <div class="header-content">
                <button class="menu-toggle" id="menuToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <div class="header-brand">
                    <div class="h3c-logo">H3C</div>
                    <h1>企业财务管理系统</h1>
                    <span class="version">v2.0</span>
                </div>
                <div class="header-actions">
                    <span class="current-time" id="currentTime"></span>
                    <div class="user-badge">
                        <span class="user-role">管理员</span>
                        <span class="user-dept">财务部</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 侧边导航栏 -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <div class="h3c-brand">
                        <span class="h3c-text">H3C</span>
                        <span class="brand-line"></span>
                    </div>
                    <div class="system-info">
                        <span class="system-name">财务管理系统</span>
                        <span class="system-subtitle">Enterprise Finance</span>
                    </div>
                </div>
            </div>
            <nav class="sidebar-nav">
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#dashboard" class="nav-link active">
                            <i class="icon">📊</i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#income" class="nav-link">
                            <i class="icon">💰</i>
                            <span>收入管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#expense" class="nav-link">
                            <i class="icon">💸</i>
                            <span>支出管理</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#reports" class="nav-link">
                            <i class="icon">📈</i>
                            <span>报表统计</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#settings" class="nav-link">
                            <i class="icon">⚙️</i>
                            <span>系统设置</span>
                        </a>
                    </li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <span class="avatar-text">管</span>
                    </div>
                    <div class="user-details">
                        <span class="user-name">张三</span>
                        <span class="user-title">财务主管</span>
                    </div>
                </div>
                <div class="h3c-footer">
                    <span class="powered-by">Powered by H3C</span>
                </div>
            </div>
        </aside>

        <!-- 侧边栏遮罩层 -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- 主要内容区域 -->
        <main class="main-content" id="mainContent">
            <!-- 仪表盘页面 -->
            <section id="dashboard" class="page active">
                <div class="page-header">
                    <h2>财务仪表盘</h2>
                    <div class="page-subtitle">H3C企业财务管理系统 - 实时数据监控</div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">💰</div>
                        <h3>总余额</h3>
                        <p class="stat-value" id="totalBalance">¥0.00</p>
                        <div class="stat-trend">较上月 +0%</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📈</div>
                        <h3>本月收入</h3>
                        <p class="stat-value income">¥0.00</p>
                        <div class="stat-trend positive">较上月 +0%</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📉</div>
                        <h3>本月支出</h3>
                        <p class="stat-value expense">¥0.00</p>
                        <div class="stat-trend negative">较上月 +0%</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <h3>交易笔数</h3>
                        <p class="stat-value">0</p>
                        <div class="stat-trend">本月累计</div>
                    </div>
                </div>

                <div class="dashboard-info">
                    <div class="info-card">
                        <h3>🏢 H3C企业级财务解决方案</h3>
                        <p>基于H3C技术架构的企业财务管理系统，提供安全、稳定、高效的财务数据管理服务。</p>
                        <ul>
                            <li>✅ 实时数据同步</li>
                            <li>✅ 多级权限管理</li>
                            <li>✅ 数据安全加密</li>
                            <li>✅ 智能报表分析</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- 收入管理页面 -->
            <section id="income" class="page">
                <h2>收入管理</h2>
                
                <!-- 添加收入表单 -->
                <div class="form-section">
                    <h3>添加收入记录</h3>
                    <form id="incomeForm" class="transaction-form">
                        <div class="form-group">
                            <label for="incomeProject">项目名称：</label>
                            <input type="text" id="incomeProject" name="project" required>
                        </div>
                        <div class="form-group">
                            <label for="incomeDate">入账时间：</label>
                            <input type="datetime-local" id="incomeDate" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="incomeAmount">金额：</label>
                            <input type="number" id="incomeAmount" name="amount" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="incomeRemark">备注：</label>
                            <textarea id="incomeRemark" name="remark" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">添加收入</button>
                    </form>
                </div>

                <!-- 收入记录列表 -->
                <div class="table-section">
                    <h3>收入记录</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>入账时间</th>
                                <th>金额</th>
                                <th>当前余额</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="incomeTableBody">
                            <!-- 动态生成的收入记录 -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 支出管理页面 -->
            <section id="expense" class="page">
                <h2>支出管理</h2>
                
                <!-- 添加支出表单 -->
                <div class="form-section">
                    <h3>添加支出记录</h3>
                    <form id="expenseForm" class="transaction-form">
                        <div class="form-group">
                            <label for="expenseProject">项目名称：</label>
                            <input type="text" id="expenseProject" name="project" required>
                        </div>
                        <div class="form-group">
                            <label for="expenseDate">支出时间：</label>
                            <input type="datetime-local" id="expenseDate" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="expenseAmount">金额：</label>
                            <input type="number" id="expenseAmount" name="amount" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label for="expenseRemark">备注：</label>
                            <textarea id="expenseRemark" name="remark" rows="3"></textarea>
                        </div>
                        <button type="submit" class="btn btn-danger">添加支出</button>
                    </form>
                </div>

                <!-- 支出记录列表 -->
                <div class="table-section">
                    <h3>支出记录</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>支出时间</th>
                                <th>金额</th>
                                <th>当前余额</th>
                                <th>备注</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="expenseTableBody">
                            <!-- 动态生成的支出记录 -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- 报表统计页面 -->
            <section id="reports" class="page">
                <h2>报表统计</h2>
                <div class="report-section">
                    <h3>财务概览</h3>
                    <div class="report-filters">
                        <label for="reportStartDate">开始日期：</label>
                        <input type="date" id="reportStartDate">
                        <label for="reportEndDate">结束日期：</label>
                        <input type="date" id="reportEndDate">
                        <button class="btn btn-secondary" onclick="generateReport()">生成报表</button>
                    </div>
                    <div id="reportContent" class="report-content">
                        <!-- 报表内容将在这里显示 -->
                    </div>
                </div>
            </section>

            <!-- 系统设置页面 -->
            <section id="settings" class="page">
                <h2>系统设置</h2>
                <div class="settings-section">
                    <h3>基本设置</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="companyName">公司名称：</label>
                            <input type="text" id="companyName" value="示例公司">
                        </div>
                        <div class="form-group">
                            <label for="currency">货币单位：</label>
                            <select id="currency">
                                <option value="CNY">人民币 (¥)</option>
                                <option value="USD">美元 ($)</option>
                                <option value="EUR">欧元 (€)</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">保存设置</button>
                    </form>
                </div>
            </section>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>
