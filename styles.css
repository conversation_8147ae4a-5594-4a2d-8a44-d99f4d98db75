/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 头部样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0 2rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    height: 60px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

.header h1 {
    font-size: 1.5rem;
    margin: 0;
    flex: 1;
    text-align: center;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.header-actions {
    display: flex;
    align-items: center;
}

.current-time {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 侧边导航栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 60px;
    width: 250px;
    height: calc(100vh - 60px);
    background-color: #2c3e50;
    color: white;
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 999;
}

.sidebar-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid #34495e;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: bold;
}

.logo .icon {
    margin-right: 0.5rem;
    font-size: 1.5rem;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    color: #bdc3c7;
    text-decoration: none;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    background-color: #34495e;
    color: white;
    border-left-color: #667eea;
}

.nav-link.active {
    background-color: #667eea;
    color: white;
    border-left-color: #5a6fd8;
}

.nav-link .icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    border-top: 1px solid #34495e;
    background-color: #2c3e50;
}

.user-info {
    display: flex;
    align-items: center;
    color: #bdc3c7;
    font-size: 0.9rem;
}

.user-info .icon {
    margin-right: 0.5rem;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    margin-left: 250px;
    margin-top: 60px;
    padding: 2rem;
    background-color: #f5f5f5;
    min-height: calc(100vh - 60px);
    transition: margin-left 0.3s ease;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-card h3 {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.stat-value.income {
    color: #28a745;
}

.stat-value.expense {
    color: #dc3545;
}

/* 表单样式 */
.form-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-section h3 {
    margin-bottom: 1rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.transaction-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* 表格样式 */
.table-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.table-section h3 {
    margin-bottom: 1rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #555;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* 报表样式 */
.report-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.report-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.report-filters label {
    font-weight: bold;
    color: #555;
}

.report-filters input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.report-content {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* 设置页面样式 */
.settings-section {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.settings-form .form-group {
    margin-bottom: 1rem;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

.main-content.expanded {
    margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .header h1 {
        font-size: 1.2rem;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .transaction-form {
        grid-template-columns: 1fr;
    }

    .report-filters {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-content {
        padding: 0 1rem;
    }

    .current-time {
        display: none;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .form-section,
    .table-section {
        padding: 1rem;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem 0.25rem;
    }
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

/* 动画效果 */
.page {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
