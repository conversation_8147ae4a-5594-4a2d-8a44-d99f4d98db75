/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 红白主题色彩变量 */
:root {
    --primary-red: #dc2626;
    --secondary-red: #b91c1c;
    --accent-red: #ef4444;
    --light-red: #fef2f2;
    --dark-red: #991b1b;
    --pure-white: #ffffff;
    --off-white: #fafafa;
    --light-gray: #f5f5f5;
    --red-gradient: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    --red-gradient-light: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

/* 头部样式 */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--red-gradient);
    color: white;
    padding: 0 2rem;
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    z-index: 1000;
    height: 70px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.header-brand {
    display: flex;
    align-items: center;
    flex: 1;
    justify-content: center;
    gap: 1rem;
}

.h3c-logo {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.75rem;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header h1 {
    font-size: 1.5rem;
    margin: 0;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.version {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
}

.menu-toggle span {
    width: 25px;
    height: 3px;
    background-color: white;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.current-time {
    font-size: 0.9rem;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-badge {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.8rem;
}

.user-role {
    font-weight: 600;
    color: white;
}

.user-dept {
    opacity: 0.8;
    font-size: 0.7rem;
}

/* 侧边导航栏样式 */
.sidebar {
    position: fixed;
    left: 0;
    top: 70px;
    width: 280px;
    height: calc(100vh - 70px);
    background: linear-gradient(180deg, var(--pure-white) 0%, var(--off-white) 100%);
    color: #333;
    overflow-y: auto;
    transition: transform 0.3s ease;
    z-index: 999;
    border-right: 3px solid var(--primary-red);
    box-shadow: 2px 0 8px rgba(220, 38, 38, 0.1);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 2px solid var(--primary-red);
    background: var(--light-red);
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.h3c-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.h3c-text {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-red);
    letter-spacing: 2px;
    text-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.brand-line {
    width: 3px;
    height: 30px;
    background: var(--primary-red);
    border-radius: 2px;
}

.system-info {
    display: flex;
    flex-direction: column;
}

.system-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-red);
    margin-bottom: 0.25rem;
}

.system-subtitle {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    color: #555;
    text-decoration: none;
    padding: 1rem 1.5rem;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
    border-radius: 0 25px 25px 0;
    margin: 0.25rem 0;
}

.nav-link:hover {
    background: linear-gradient(90deg, var(--light-red) 0%, rgba(220, 38, 38, 0.1) 100%);
    color: var(--primary-red);
    border-left-color: var(--accent-red);
    transform: translateX(5px);
}

.nav-link.active {
    background: linear-gradient(90deg, var(--primary-red) 0%, var(--secondary-red) 100%);
    color: white;
    border-left-color: var(--accent-red);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.nav-link .icon {
    margin-right: 0.75rem;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    border-top: 2px solid var(--primary-red);
    background: linear-gradient(180deg, var(--light-red) 0%, var(--pure-white) 100%);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--red-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    border: 2px solid var(--accent-red);
}

.avatar-text {
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    color: var(--dark-red);
    font-size: 0.9rem;
}

.user-title {
    color: #666;
    font-size: 0.8rem;
}

.h3c-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.powered-by {
    font-size: 0.7rem;
    color: var(--primary-red);
    font-weight: 500;
    letter-spacing: 1px;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    margin-left: 280px;
    margin-top: 70px;
    padding: 2rem;
    background: var(--red-gradient-light);
    min-height: calc(100vh - 70px);
    transition: margin-left 0.3s ease;
}

.page {
    display: none;
}

.page.active {
    display: block;
}

/* 页面头部样式 */
.page-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--pure-white);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    border-left: 4px solid var(--primary-red);
}

.page-header h2 {
    margin: 0 0 0.5rem 0;
    color: var(--dark-red);
    font-size: 2rem;
    font-weight: 600;
}

.page-subtitle {
    color: #666;
    font-size: 1rem;
    font-style: italic;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--pure-white);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    text-align: center;
    border-left: 4px solid var(--primary-red);
    border-top: 1px solid rgba(220, 38, 38, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.25);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.stat-trend {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    color: #666;
    font-weight: 500;
}

.stat-trend.positive {
    color: #28a745;
}

.stat-trend.negative {
    color: #dc3545;
}

/* 仪表盘信息卡片 */
.dashboard-info {
    margin-top: 2rem;
}

.info-card {
    background: var(--pure-white);
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    border-left: 4px solid var(--primary-red);
}

.info-card h3 {
    color: var(--primary-red);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.info-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.info-card ul {
    list-style: none;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
}

.info-card li {
    color: #555;
    font-weight: 500;
}

.stat-card h3 {
    color: #666;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

.stat-value.income {
    color: #28a745;
}

.stat-value.expense {
    color: #dc3545;
}

/* 表单样式 */
.form-section {
    background: var(--pure-white);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-red);
}

.form-section h3 {
    margin-bottom: 1rem;
    color: var(--dark-red);
    border-bottom: 2px solid var(--primary-red);
    padding-bottom: 0.5rem;
}

.transaction-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-red);
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

/* 按钮样式 */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--red-gradient);
    color: white;
    border: 1px solid var(--primary-red);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-red) 0%, var(--primary-red) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* 表格样式 */
.table-section {
    background: var(--pure-white);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-red);
}

.table-section h3 {
    margin-bottom: 1rem;
    color: var(--dark-red);
    border-bottom: 2px solid var(--primary-red);
    padding-bottom: 0.5rem;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.data-table th {
    background-color: #f8f9fa;
    font-weight: bold;
    color: #555;
}

.data-table tr:hover {
    background-color: #f8f9fa;
}

/* 报表样式 */
.report-section {
    background: var(--pure-white);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    border-left: 4px solid var(--primary-red);
}

.report-filters {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.report-filters label {
    font-weight: bold;
    color: #555;
}

.report-filters input {
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.report-content {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* 设置页面样式 */
.settings-section {
    background: var(--pure-white);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    border-left: 4px solid var(--primary-red);
}

.settings-form .form-group {
    margin-bottom: 1rem;
}

/* 侧边栏折叠状态 */
.sidebar.collapsed {
    transform: translateX(-100%);
}

.main-content.expanded {
    margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .header-brand h1 {
        font-size: 1rem;
    }

    .h3c-logo {
        font-size: 1.5rem;
        padding: 0.2rem 0.5rem;
    }

    .version {
        display: none;
    }

    .user-badge {
        display: none;
    }

    .sidebar {
        width: 280px;
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .transaction-form {
        grid-template-columns: 1fr;
    }

    .report-filters {
        flex-direction: column;
        align-items: flex-start;
    }

    .header-content {
        padding: 0 1rem;
    }

    .current-time {
        display: none;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .form-section,
    .table-section {
        padding: 1rem;
    }

    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 0.5rem 0.25rem;
    }
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}

/* 动画效果 */
.page {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
