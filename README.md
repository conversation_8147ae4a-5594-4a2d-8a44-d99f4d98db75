# ERP出入账系统

一个简单易用的ERP出入账管理系统前端界面，使用HTML、CSS和JavaScript构建。

## 功能特性

### 界面设计
- **侧边导航栏**：专业的左侧导航菜单，包含图标和文字
- **响应式布局**：桌面端固定侧边栏，移动端可折叠菜单
- **实时时间显示**：头部显示当前系统时间
- **用户信息**：侧边栏底部显示当前用户信息

### 核心功能
- **仪表盘**：显示总余额、本月收入、本月支出和交易笔数统计
- **收入管理**：添加、查看和删除收入记录
- **支出管理**：添加、查看和删除支出记录
- **报表统计**：按日期范围生成财务报表
- **系统设置**：基本配置管理

### 数据字段
每条交易记录包含以下字段：
- **项目名称**：交易相关的项目或分类
- **入账时间**：交易发生的具体时间
- **金额**：交易金额（支持小数）
- **余额**：交易后的账户余额
- **备注**：可选的备注信息

## 文件结构

```
出入账系统/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript功能文件
└── README.md           # 说明文档
```

## 使用方法

### 1. 启动系统
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 导航操作

#### 桌面端导航
- 左侧固定侧边栏，点击菜单项直接切换页面
- 当前页面在侧边栏中高亮显示

#### 移动端导航
- 点击左上角汉堡菜单按钮打开侧边栏
- 点击菜单项后自动关闭侧边栏
- 点击遮罩层可关闭侧边栏

### 3. 主要操作

#### 添加收入记录
1. 点击侧边栏中的"💰 收入管理"
2. 填写项目名称、入账时间、金额和备注
3. 点击"添加收入"按钮

#### 添加支出记录
1. 点击侧边栏中的"💸 支出管理"
2. 填写项目名称、支出时间、金额和备注
3. 点击"添加支出"按钮

#### 查看统计信息
1. 点击侧边栏中的"📊 仪表盘"
2. 查看总余额、本月收入、本月支出等统计数据

#### 生成报表
1. 点击侧边栏中的"📈 报表统计"
2. 选择开始和结束日期
3. 点击"生成报表"按钮

#### 删除记录
在收入管理或支出管理页面的表格中，点击对应记录的"删除"按钮。

## 技术特点

### 前端技术
- **HTML5**：语义化标签，良好的结构
- **CSS3**：响应式设计，现代化界面
- **JavaScript ES6+**：模块化代码，良好的用户体验

### 数据存储
- 使用浏览器的 `localStorage` 进行本地数据存储
- 数据在浏览器关闭后仍然保留
- 支持数据的增删改查操作

### 响应式设计
- 支持桌面端和移动端访问
- 自适应不同屏幕尺寸
- 优化的移动端交互体验

## 界面预览

### 整体布局
- **侧边导航栏**：左侧固定导航，包含图标和文字标识
- **顶部标题栏**：显示系统名称和实时时间
- **主内容区域**：右侧主要功能区域

### 仪表盘
- 显示关键财务指标
- 卡片式布局，信息清晰
- 实时数据更新

### 收入/支出管理
- 表单式数据录入
- 表格式数据展示
- 实时余额计算
- 操作按钮便捷

### 报表统计
- 日期范围筛选
- 多维度数据分析
- 直观的数据展示

### 响应式特性
- **桌面端**：侧边栏固定显示，内容区域自适应
- **移动端**：侧边栏可折叠，汉堡菜单控制
- **平板端**：自动适配中等屏幕尺寸

## 浏览器兼容性

支持现代浏览器：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 扩展建议

### 后续可以添加的功能
1. **数据导出**：支持Excel、PDF格式导出
2. **数据备份**：云端数据同步
3. **用户权限**：多用户管理
4. **图表展示**：收支趋势图表
5. **分类管理**：项目分类和标签
6. **预算管理**：预算设置和预警
7. **发票管理**：发票上传和关联

### 技术优化
1. **后端集成**：连接数据库和API
2. **状态管理**：使用现代前端框架
3. **性能优化**：代码分割和懒加载
4. **安全加固**：数据加密和验证

## 注意事项

1. **数据安全**：当前版本数据存储在浏览器本地，清除浏览器数据会导致数据丢失
2. **备份建议**：重要数据请定期手动备份
3. **浏览器限制**：localStorage有存储大小限制（通常5-10MB）

## 联系方式

如有问题或建议，请联系开发团队。
